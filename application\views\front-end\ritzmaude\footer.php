<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- Footer Section -->
<div class="shopify-section shopify-section-group-footer-group">
    
    <!-- Footer Styles -->
    <style>
        .footer {
            margin-top: 0px;
            background-color: #8B1538;
            color: #fff;
        }
        .section-footer-padding {
            padding-top: 50px;
            padding-bottom: 30px;
        }
        @media screen and (min-width: 750px) {
            .footer {
                margin-top: 0px;
            }
            .section-footer-padding {
                padding-top: 60px;
                padding-bottom: 40px;
            }
        }

        /* Original Red/Burgundy Color Scheme */
        .footer .link--text {
            color: #fff;
            text-decoration: none;
            font-size: 14px;
            line-height: 1.6;
        }
        .footer .link--text:hover {
            color: #D4AF37;
        }
        .footer-block__heading {
            color: #fff;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .newsletter-form__button {
            background-color: transparent;
            color: #fff;
            border: 1px solid #fff;
            padding: 12px 20px;
        }
        .newsletter-form__button:hover {
            background-color: #fff;
            color: #8B1538;
        }
        .field__input {
            background-color: transparent;
            color: #fff;
            border: 1px solid #fff;
            padding: 12px 15px;
        }
        .field__input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        .field__input:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
        }
        .footer__content-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 40px;
            padding-top: 20px;
        }
        .copyright__content {
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }
        .copyright__content a {
            color: #fff;
        }
        .footer-block__image-wrapper {
            text-align: center;
        }
        .footer-block__image-wrapper img {
            filter: brightness(0) invert(1);
        }
    </style>

    <footer class="footer color-scheme-2 gradient section-footer-padding">
        <div class="footer__content-top page-width">
            <div class="footer__blocks-wrapper grid grid--1-col grid--2-col grid--4-col-tablet scroll-trigger animate--slide-in" data-cascade>

                <!-- Footer Logo -->
                <div class="footer-block grid__item scroll-trigger animate--slide-in" data-cascade style="--animation-order: 1;">
                    <div class="footer-block__details-content footer-block-image">
                        <div class="footer-block__image-wrapper" style="max-width: min(100%, 120px); margin-bottom: 30px;">
                            <?php if (isset($web_settings['web_logo']) && !empty($web_settings['web_logo'])): ?>
                                <img src="<?= base_url($web_settings['web_logo']) ?>" alt="<?= isset($web_settings['site_title']) ? $web_settings['site_title'] : 'RITZ MAUDE' ?>" loading="lazy" width="120" height="120">
                            <?php else: ?>
                                <img src="<?= base_url('assets/front_end/Ritz Maude Frontpage/images/files-PNG-02_LOGO_3_6ddc82c4-18bb-404f-91e7-3e322462490f.png') ?>" alt="RITZ MAUDE" loading="lazy" width="120" height="120">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Brand Links -->
                <div class="footer-block grid__item footer-block--menu scroll-trigger animate--slide-in" data-cascade style="--animation-order: 2;">
                    <ul class="footer-block__details-content list-unstyled">
                        <li>
                            <a href="<?= base_url('about') ?>" class="link link--text list-menu__item list-menu__item--link">
                                THE BRAND
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('stockists') ?>" class="link link--text list-menu__item list-menu__item--link">
                                STOCKISTS
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('contact') ?>" class="link link--text list-menu__item list-menu__item--link">
                                CONTACT
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('faqs') ?>" class="link link--text list-menu__item list-menu__item--link">
                                FAQs
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Support Links -->
                <div class="footer-block grid__item footer-block--menu scroll-trigger animate--slide-in" data-cascade style="--animation-order: 3;">
                    <ul class="footer-block__details-content list-unstyled">
                        <li>
                            <a href="<?= base_url('sizing') ?>" class="link link--text list-menu__item list-menu__item--link">
                                SIZE GUIDE
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('shipping-and-returns') ?>" class="link link--text list-menu__item list-menu__item--link">
                                SHIPPING &amp; RETURNS
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('privacy-policy') ?>" class="link link--text list-menu__item list-menu__item--link">
                                PRIVACY POLICY
                            </a>
                        </li>
                        <li>
                            <a href="<?= base_url('terms-and-conditions') ?>" class="link link--text list-menu__item list-menu__item--link">
                                TERMS &amp; CONDITIONS
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Shop Links -->
                <div class="footer-block grid__item footer-block--menu scroll-trigger animate--slide-in" data-cascade style="--animation-order: 4;">
                    <ul class="footer-block__details-content list-unstyled">
                        <?php if (isset($categories) && !empty($categories)): ?>
                            <?php foreach (array_slice($categories, 0, 4) as $category): ?>
                                <li>
                                    <a href="<?= base_url('products?category=' . $category['slug']) ?>" class="link link--text list-menu__item list-menu__item--link">
                                        <?= strtoupper($category['name']) ?>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <li>
                                <a href="<?= base_url('products?filter=men') ?>" class="link link--text list-menu__item list-menu__item--link">
                                    MEN
                                </a>
                            </li>
                            <li>
                                <a href="<?= base_url('products?filter=women') ?>" class="link link--text list-menu__item list-menu__item--link">
                                    WOMEN
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Newsletter Section -->
            <div class="footer-block--newsletter scroll-trigger animate--slide-in" data-cascade>
                <div class="footer-block__newsletter">
                    <h2 class="footer-block__heading inline-richtext">SUBSCRIBE TO OUR NEWSLETTER</h2>
                    <form method="post" action="<?= base_url('newsletter/subscribe') ?>" id="ContactFooter" accept-charset="UTF-8" class="footer__newsletter newsletter-form">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">
                        <input type="hidden" name="form_type" value="newsletter">
                        
                        <div class="newsletter-form__field-wrapper">
                            <div class="field">
                                <input id="NewsletterForm--footer" type="email" name="email" class="field__input" value="" aria-required="true" autocorrect="off" autocapitalize="off" autocomplete="email" placeholder="Email" required>
                                <label class="field__label" for="NewsletterForm--footer">
                                    Email
                                </label>
                                <button type="submit" class="newsletter-form__button field__button" name="commit" id="Subscribe" aria-label="Subscribe">
                                    <svg viewbox="0 0 14 10" fill="none" aria-hidden="true" focusable="false" class="icon icon-arrow" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.537.808a.5.5 0 01.817-.162l4 4a.5.5 0 010 .708l-4 4a.5.5 0 11-.708-.708L11.793 5.5H1a.5.5 0 010-1h10.793L8.646 1.354a.5.5 0 01-.109-.546z" fill="currentColor"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Social Media Links -->
                <div class="footer__list-social list-social">
                    <ul class="list list-social__list" role="list">
                        <?php if (isset($web_settings['instagram_url']) && !empty($web_settings['instagram_url'])): ?>
                            <li class="list-social__item">
                                <a href="<?= $web_settings['instagram_url'] ?>" class="link list-social__link" target="_blank" rel="noopener">
                                    <svg aria-hidden="true" focusable="false" class="icon icon-instagram" viewbox="0 0 20 20">
                                        <path fill="currentColor" fill-rule="evenodd" d="M13.23 3.492c-.84-.037-1.096-.046-3.23-.046-2.144 0-2.39.01-3.238.055-.776.027-1.195.164-1.487.273a2.43 2.43 0 0 0-.912.593 2.486 2.486 0 0 0-.602.922c-.11.282-.238.702-.274 1.486-.046.84-.046 1.095-.046 3.23 0 2.134.01 2.39.046 ************.097 1.016.274 1.495.145.365.319.639.602.913.282.282.538.456.92.602.474.176.974.268 1.479.273.848.046 1.103.046 3.238.046 2.134 0 2.39-.01 3.23-.046.784-.036 1.203-.164 1.486-.273.374-.146.648-.329.921-.602.283-.283.447-.548.602-.922.177-.476.27-.979.274-1.486.037-.84.046-1.095.046-3.23 0-2.134-.01-2.39-.055-3.229-.027-.784-.164-1.204-.274-1.495a2.43 2.43 0 0 0-.593-.913 2.604 2.604 0 0 0-.92-.602c-.284-.11-.703-.237-1.488-.273ZM6.697 2.05c.857-.036 1.131-.045 3.302-.045 1.1-.014 2.202.001 3.302.045.664.014 1.321.14 1.943.374a3.968 3.968 0 0 1 1.414.922c.41.397.728.88.93 1.414.23.622.354 1.279.365 1.942C18 7.56 18 7.824 18 10.005c0 2.17-.01 2.444-.046 3.292-.036.858-.173 1.442-.374 1.943-.2.53-.474.976-.92 1.423a3.896 3.896 0 0 1-1.415.922c-.51.191-1.095.337-1.943.374-.857.036-1.122.045-3.302.045-2.171 0-2.445-.009-3.302-.055-.849-.027-1.432-.164-1.943-.364a4.152 4.152 0 0 1-1.414-.922 4.128 4.128 0 0 1-.93-1.423c-.183-.51-.329-1.085-.365-1.943C2.009 12.45 2 12.167 2 10.004c0-2.161 0-2.435.055-3.302.027-.848.164-1.432.365-1.942a4.44 4.44 0 0 1 .92-1.414 4.18 4.18 0 0 1 1.415-.93c.51-.183 1.094-.33 1.943-.366Zm.427 4.806a4.105 4.105 0 1 1 5.805 5.805 4.105 4.105 0 0 1-5.805-5.805Zm1.882 5.371a2.668 2.668 0 1 0 2.042-4.93 2.668 2.668 0 0 0-2.042 4.93Zm5.922-5.942a.958.958 0 1 1-1.355-1.355.958.958 0 0 1 1.355 1.355Z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="visually-hidden">Instagram</span>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="list-social__item">
                                <a href="https://www.instagram.com/shopritzmaude/" class="link list-social__link" target="_blank" rel="noopener">
                                    <svg aria-hidden="true" focusable="false" class="icon icon-instagram" viewbox="0 0 20 20">
                                        <path fill="currentColor" fill-rule="evenodd" d="M13.23 3.492c-.84-.037-1.096-.046-3.23-.046-2.144 0-2.39.01-3.238.055-.776.027-1.195.164-1.487.273a2.43 2.43 0 0 0-.912.593 2.486 2.486 0 0 0-.602.922c-.11.282-.238.702-.274 1.486-.046.84-.046 1.095-.046 3.23 0 2.134.01 2.39.046 ************.097 1.016.274 1.495.145.365.319.639.602.913.282.282.538.456.92.602.474.176.974.268 1.479.273.848.046 1.103.046 3.238.046 2.134 0 2.39-.01 3.23-.046.784-.036 1.203-.164 1.486-.273.374-.146.648-.329.921-.602.283-.283.447-.548.602-.922.177-.476.27-.979.274-1.486.037-.84.046-1.095.046-3.23 0-2.134-.01-2.39-.055-3.229-.027-.784-.164-1.204-.274-1.495a2.43 2.43 0 0 0-.593-.913 2.604 2.604 0 0 0-.92-.602c-.284-.11-.703-.237-1.488-.273ZM6.697 2.05c.857-.036 1.131-.045 3.302-.045 1.1-.014 2.202.001 3.302.045.664.014 1.321.14 1.943.374a3.968 3.968 0 0 1 1.414.922c.41.397.728.88.93 1.414.23.622.354 1.279.365 1.942C18 7.56 18 7.824 18 10.005c0 2.17-.01 2.444-.046 3.292-.036.858-.173 1.442-.374 1.943-.2.53-.474.976-.92 1.423a3.896 3.896 0 0 1-1.415.922c-.51.191-1.095.337-1.943.374-.857.036-1.122.045-3.302.045-2.171 0-2.445-.009-3.302-.055-.849-.027-1.432-.164-1.943-.364a4.152 4.152 0 0 1-1.414-.922 4.128 4.128 0 0 1-.93-1.423c-.183-.51-.329-1.085-.365-1.943C2.009 12.45 2 12.167 2 10.004c0-2.161 0-2.435.055-3.302.027-.848.164-1.432.365-1.942a4.44 4.44 0 0 1 .92-1.414 4.18 4.18 0 0 1 1.415-.93c.51-.183 1.094-.33 1.943-.366Zm.427 4.806a4.105 4.105 0 1 1 5.805 5.805 4.105 4.105 0 0 1-5.805-5.805Zm1.882 5.371a2.668 2.668 0 1 0 2.042-4.93 2.668 2.668 0 0 0-2.042 4.93Zm5.922-5.942a.958.958 0 1 1-1.355-1.355.958.958 0 0 1 1.355 1.355Z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="visually-hidden">Instagram</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php if (isset($web_settings['facebook_url']) && !empty($web_settings['facebook_url'])): ?>
                            <li class="list-social__item">
                                <a href="<?= $web_settings['facebook_url'] ?>" class="link list-social__link" target="_blank" rel="noopener">
                                    <svg aria-hidden="true" focusable="false" class="icon icon-facebook" viewbox="0 0 20 20">
                                        <path fill="currentColor" d="M18.05.811q.439 0 .744.305t.305.744v16.637q0 .439-.305.744t-.744.305h-4.732v-7.221h2.415l.342-2.854h-2.757v-1.83q0-.659.293-1t1.073-.342h1.488V3.762q-.976-.098-2.171-.098-1.634 0-2.635.964t-1 2.72V9.47H7.951v2.854h2.415v7.221H1.413q-.439 0-.744-.305t-.305-.744V1.859q0-.439.305-.744T1.413.81H18.05Z"></path>
                                    </svg>
                                    <span class="visually-hidden">Facebook</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer__content-bottom scroll-trigger animate--slide-in" data-cascade>
            <div class="footer__content-bottom-wrapper page-width">
                <div class="footer__column footer__localization isolate"></div>
                <div class="footer__column footer__column--info">
                    <div class="footer__payment">
                        <span class="visually-hidden">Payment methods</span>
                        <ul class="list list-payment" role="list">
                            <!-- Payment methods can be added here -->
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer__content-bottom-wrapper page-width">
                <div class="footer__copyright caption">
                    <small class="copyright__content">&copy; <?= date('Y') ?>, <a href="<?= base_url() ?>" title=""><?= isset($web_settings['site_title']) ? $web_settings['site_title'] : 'RITZ MAUDE' ?></a></small>
                    <ul class="policies list-unstyled">
                        <li>
                            <small class="copyright__content"><a href="<?= base_url('privacy-policy') ?>">Privacy policy</a></small>
                        </li>
                        <li>
                            <small class="copyright__content"><a href="<?= base_url('terms-and-conditions') ?>">Terms & Conditions</a></small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
</div>

<!-- Newsletter Subscription Script -->
<script>
$(document).ready(function() {
    $('#ContactFooter').on('submit', function(e) {
        e.preventDefault();
        
        var email = $(this).find('input[name="email"]').val();
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.html();
        
        if (!email) {
            alert('Please enter your email address');
            return;
        }
        
        submitBtn.prop('disabled', true).html('<span class="loading-spinner"></span>');
        
        $.ajax({
            url: '<?= base_url('newsletter/subscribe') ?>',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash,
                email: email
            },
            success: function(response) {
                if (response.error === false) {
                    alert('Thank you for subscribing to our newsletter!');
                    $('#ContactFooter')[0].reset();
                } else {
                    alert(response.message || 'Failed to subscribe. Please try again.');
                }
            },
            error: function() {
                alert('An error occurred. Please try again.');
            },
            complete: function() {
                submitBtn.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
