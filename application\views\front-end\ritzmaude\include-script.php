<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>

<!-- jQuery (if not already loaded) -->
<script src="<?= base_url('assets/front_end/js/jquery-3.6.0.min.js') ?>"></script>

<!-- API Wrapper -->
<script src="<?= THEME_ASSETS_URL ?>js/api.js"></script>

<!-- Performance Optimization -->
<script src="<?= THEME_ASSETS_URL ?>js/performance.js"></script>

<!-- Original Ritz Maude JavaScript files -->
<script src="<?= base_url('assets/front_end/Ritz Maude Frontpage/js/assets-constants.js') ?>" defer></script>
<script src="<?= base_url('assets/front_end/Ritz Maude Frontpage/js/assets-pubsub.js') ?>" defer></script>
<script src="<?= base_url('assets/front_end/Ritz Maude Frontpage/js/assets-global.js') ?>" defer></script>
<script src="<?= base_url('assets/front_end/Ritz Maude Frontpage/js/assets-animations.js') ?>" defer></script>

<!-- Localization form script -->
<script src="<?= base_url('assets/front_end/Ritz Maude Frontpage/js/assets-localization-form.js') ?>" defer></script>

<!-- Custom eShop Integration Scripts -->
<script>
// Global variables for eShop functionality
window.eShop = {
    baseUrl: '<?= base_url() ?>',
    apiUrl: '<?= base_url('app/v1/api/') ?>',
    csrfName: '<?= $this->security->get_csrf_token_name() ?>',
    csrfHash: '<?= $this->security->get_csrf_hash() ?>',
    isLoggedIn: <?= isset($is_logged_in) ? ($is_logged_in ? 'true' : 'false') : 'false' ?>,
    userId: <?= isset($user) && !empty($user) ? $user->id : 'null' ?>,
    currency: '<?= isset($web_settings['currency']) ? $web_settings['currency'] : 'NGN' ?>',
    currencySymbol: '<?= isset($web_settings['currency_symbol']) ? $web_settings['currency_symbol'] : '₦' ?>'
};

// Cart functionality
window.eShop.cart = {
    // Add item to cart
    addToCart: function(productId, variantId, quantity) {
        quantity = quantity || 1;
        
        $.ajax({
            url: window.eShop.apiUrl + 'manage_cart',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash,
                product_id: productId,
                product_variant_id: variantId,
                qty: quantity,
                is_saved_for_later: 0
            },
            beforeSend: function() {
                $('.add-to-cart-btn').prop('disabled', true).html('<span class="loading-spinner"></span> Adding...');
            },
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.showNotification('Product added to cart successfully!', 'success');
                    window.eShop.cart.updateCartCount();
                } else {
                    window.eShop.cart.showNotification(response.message || 'Failed to add product to cart', 'error');
                }
            },
            error: function() {
                window.eShop.cart.showNotification('An error occurred. Please try again.', 'error');
            },
            complete: function() {
                $('.add-to-cart-btn').prop('disabled', false).html('Add to Cart');
            }
        });
    },
    
    // Remove item from cart
    removeFromCart: function(cartId) {
        $.ajax({
            url: window.eShop.apiUrl + 'remove_from_cart',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash,
                id: cartId
            },
            success: function(response) {
                if (response.error === false) {
                    window.eShop.cart.showNotification('Product removed from cart', 'success');
                    window.eShop.cart.updateCartCount();
                    location.reload(); // Refresh cart page
                } else {
                    window.eShop.cart.showNotification(response.message || 'Failed to remove product', 'error');
                }
            },
            error: function() {
                window.eShop.cart.showNotification('An error occurred. Please try again.', 'error');
            }
        });
    },
    
    // Update cart count in header
    updateCartCount: function() {
        $.ajax({
            url: window.eShop.apiUrl + 'get_user_cart',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash
            },
            success: function(response) {
                if (response.error === false && response.data) {
                    var totalItems = response.data.length;
                    $('.cart-count').text(totalItems);
                    $('.cart-count-bubble').text(totalItems).toggle(totalItems > 0);
                }
            }
        });
    },
    
    // Show notification
    showNotification: function(message, type) {
        var notification = $('<div class="cart-notification ' + type + '">' + message + '</div>');
        $('body').append(notification);
        notification.fadeIn().delay(3000).fadeOut(function() {
            $(this).remove();
        });
    }
};

// Product functionality
window.eShop.product = {
    // Load products for a category
    loadProducts: function(categoryId, containerId, limit) {
        limit = limit || 8;
        
        $.ajax({
            url: window.eShop.apiUrl + 'get_products',
            type: 'POST',
            data: {
                [window.eShop.csrfName]: window.eShop.csrfHash,
                category_id: categoryId,
                limit: limit,
                sort: 'new'
            },
            beforeSend: function() {
                $(containerId).html('<div class="loading-spinner"></div>');
            },
            success: function(response) {
                if (response.error === false && response.data) {
                    window.eShop.product.renderProducts(response.data, containerId);
                } else {
                    $(containerId).html('<p>No products found</p>');
                }
            },
            error: function() {
                $(containerId).html('<p>Error loading products</p>');
            }
        });
    },
    
    // Render products HTML
    renderProducts: function(products, containerId) {
        var html = '';
        products.forEach(function(product) {
            var imageUrl = product.image ? window.eShop.baseUrl + product.image : window.eShop.baseUrl + 'assets/no-image.png';
            var price = window.eShop.currencySymbol + parseFloat(product.price).toLocaleString();
            
            html += '<div class="product-card">';
            html += '<a href="' + window.eShop.baseUrl + 'products/details/' + product.slug + '">';
            html += '<img src="' + imageUrl + '" alt="' + product.name + '" loading="lazy">';
            html += '<h3>' + product.name + '</h3>';
            html += '<p class="price">' + price + '</p>';
            html += '</a>';
            html += '<button class="add-to-cart-btn" onclick="window.eShop.cart.addToCart(' + product.id + ', ' + (product.variants && product.variants[0] ? product.variants[0].id : 'null') + ', 1)">Add to Cart</button>';
            html += '</div>';
        });
        
        $(containerId).html(html);
    }
};

// Initialize on document ready
$(document).ready(function() {
    // Update cart count on page load
    if (window.eShop.isLoggedIn) {
        window.eShop.cart.updateCartCount();
    }
    
    // Handle form submissions with CSRF token
    $('form').on('submit', function() {
        if (!$(this).find('input[name="' + window.eShop.csrfName + '"]').length) {
            $(this).append('<input type="hidden" name="' + window.eShop.csrfName + '" value="' + window.eShop.csrfHash + '">');
        }
    });
    
    // Initialize any dynamic content
    if (typeof initializeDynamicContent === 'function') {
        initializeDynamicContent();
    }
});
</script>

<!-- Theme specific JavaScript -->
<script>
document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
</script>
