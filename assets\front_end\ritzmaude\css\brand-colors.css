/**
 * Ritz Maude Brand Colors - Original Red/Burgundy Theme
 * Maintains the original design color scheme
 */

/* Root Variables for Brand Colors */
:root {
    --brand-red: #8B1538;
    --brand-red-dark: #6B0F2A;
    --brand-red-light: #A01B42;
    --brand-gold: #D4AF37;
    --brand-black: #000000;
    --brand-dark-gray: #333333;
    --brand-light-gray: #cccccc;
    --brand-white: #ffffff;
}

/* Primary Color Overrides */
.color-scheme-1 {
    --color-foreground: 0, 0, 0;
    --color-background: 255, 255, 255;
    --color-link: 139, 21, 56; /* Original Red */
    --color-button: 139, 21, 56; /* Original Red */
    --color-button-text: 255, 255, 255; /* White text on red */
}

.color-scheme-2 {
    --color-foreground: 255, 255, 255;
    --color-background: 139, 21, 56; /* Original Red Background */
    --color-link: 212, 175, 55; /* Gold for links on red */
    --color-button: 212, 175, 55; /* Gold buttons on red background */
    --color-button-text: 0, 0, 0; /* Black text on gold */
}

/* Button Styling */
.button,
.btn,
.shopify-payment-button__button--unbranded {
    background-color: var(--brand-red) !important;
    color: var(--brand-white) !important;
    border-color: var(--brand-red) !important;
}

.button:hover,
.btn:hover,
.shopify-payment-button__button--unbranded:hover {
    background-color: var(--brand-red-dark) !important;
    color: var(--brand-white) !important;
}

.button--secondary {
    background-color: transparent !important;
    color: var(--brand-red) !important;
    border: 2px solid var(--brand-red) !important;
}

.button--secondary:hover {
    background-color: var(--brand-red) !important;
    color: var(--brand-white) !important;
}

/* Link Colors */
a,
.link {
    color: var(--brand-gold);
}

a:hover,
.link:hover {
    color: var(--brand-gold-dark);
}

/* Header Styling */
.header {
    background-color: var(--brand-white);
    border-bottom: 1px solid var(--brand-light-gray);
}

.header__icon {
    color: var(--brand-black);
}

.header__icon:hover {
    color: var(--brand-gold);
}

/* Cart Count Bubble */
.cart-count-bubble {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
    font-size: 0.75rem !important;
    width: 16px !important;
    height: 16px !important;
    bottom: 0.5rem !important;
    left: 1.8rem !important;
}

/* Cart Icon Sizing */
.header__icon--cart {
    width: 44px !important;
    height: 44px !important;
}

.header__icon--cart .icon {
    width: 20px !important;
    height: 20px !important;
}

/* Navigation */
.header__menu-item {
    color: var(--brand-black);
}

.header__menu-item:hover {
    color: var(--brand-gold);
}

/* Footer Styling */
.footer {
    background-color: var(--brand-black) !important;
    color: var(--brand-white) !important;
}

.footer .link--text {
    color: var(--brand-white) !important;
}

.footer .link--text:hover {
    color: var(--brand-gold) !important;
}

.footer-block__heading {
    color: var(--brand-gold) !important;
}

/* Form Elements */
.field__input,
.select__select {
    border-color: var(--brand-gold);
}

.field__input:focus,
.select__select:focus {
    border-color: var(--brand-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

/* Newsletter Form */
.newsletter-form__button {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
}

.newsletter-form__button:hover {
    background-color: var(--brand-gold-dark) !important;
}

/* Product Cards */
.card-wrapper .card {
    border: 1px solid var(--brand-light-gray);
}

.card-wrapper .card:hover {
    border-color: var(--brand-gold);
    box-shadow: 0 4px 8px rgba(212, 175, 55, 0.2);
}

.price {
    color: var(--brand-gold);
    font-weight: bold;
}

/* Add to Cart Buttons */
.product-form__cart-submit {
    background-color: var(--brand-gold) !important;
    color: var(--brand-black) !important;
}

.product-form__cart-submit:hover {
    background-color: var(--brand-gold-dark) !important;
}

/* Quantity Selector */
.quantity {
    border-color: var(--brand-gold);
}

.quantity__button {
    color: var(--brand-gold);
}

.quantity__button:hover {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

/* Badges and Labels */
.badge {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

.badge--sale {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

/* Pagination */
.pagination__item {
    border-color: var(--brand-gold);
}

.pagination__item--current {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

.pagination__item:hover {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

/* Breadcrumbs */
.breadcrumb__link {
    color: var(--brand-gold);
}

.breadcrumb__link:hover {
    color: var(--brand-gold-dark);
}

/* Filters and Sorting */
.facets__disclosure {
    border-color: var(--brand-gold);
}

.facets__summary {
    color: var(--brand-gold);
}

/* Collection Hero */
.collection-hero__title {
    color: var(--brand-black);
}

.collection-hero__description {
    color: var(--brand-dark-gray);
}

/* Search */
.search__button {
    color: var(--brand-gold);
}

.search__input:focus {
    border-color: var(--brand-gold);
}

/* Notifications */
.notification {
    border-left: 4px solid var(--brand-gold);
}

.notification--success {
    background-color: rgba(212, 175, 55, 0.1);
    border-left-color: var(--brand-gold);
}

.notification--error {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
}

/* Loading States */
.loading__spinner .path {
    stroke: var(--brand-gold);
}

/* Focus States */
*:focus-visible {
    outline-color: var(--brand-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
}

/* Mobile Menu */
.menu-drawer {
    background-color: var(--brand-white);
}

.menu-drawer__menu-item {
    color: var(--brand-black);
}

.menu-drawer__menu-item:hover {
    color: var(--brand-gold);
    background-color: rgba(212, 175, 55, 0.1);
}

/* Announcement Bar */
.announcement-bar {
    background-color: var(--brand-gold);
    color: var(--brand-black);
}

.announcement-bar__link {
    color: var(--brand-black);
}

.announcement-bar__link:hover {
    color: var(--brand-white);
}

/* Social Media Icons */
.list-social__link {
    color: var(--brand-gold);
}

.list-social__link:hover {
    color: var(--brand-gold-dark);
}

/* Override any remaining red/wine colors */
.text-red,
.color-red,
.bg-red {
    color: var(--brand-gold) !important;
    background-color: var(--brand-gold) !important;
}

/* Ensure proper contrast */
.text-on-gold {
    color: var(--brand-black) !important;
}

.text-on-black {
    color: var(--brand-white) !important;
}

/* Custom animations with brand colors */
@keyframes goldPulse {
    0% { box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(212, 175, 55, 0); }
    100% { box-shadow: 0 0 0 0 rgba(212, 175, 55, 0); }
}

.pulse-gold {
    animation: goldPulse 2s infinite;
}
